#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
from docx import Document
import os

def test_fixed_extract(file_path):
    """测试修复后的题目提取功能"""
    print(f"正在测试文件：{file_path}")
    
    try:
        doc = Document(file_path)
        questions = []
        current_question = {"content": "", "options": [], "answer": "", "type": "unknown", "explanation": ""}
        in_question = False
        
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if not text:
                continue
            
            # 跳过文档头部信息
            if text in ["窗体顶端", "窗体底端"] or text.startswith("窗体"):
                continue
            
            # 检测题目开始 - 只匹配数字开头的题目
            if re.match(r'^\d+[、\.\)]', text):
                # 保存上一题
                if current_question["content"] and in_question and current_question["answer"]:
                    questions.append(current_question)
                
                # 开始新题目
                current_question = {
                    "content": text,
                    "options": [],
                    "answer": "",
                    "type": "unknown",
                    "explanation": ""
                }
                in_question = True
                
            # 检测选项（A-D，支持更多格式）
            elif re.match(r'^[A-D][、\.\)]', text) and in_question:
                current_question["options"].append(text)
                
            # 检测答案行 - 支持多种格式
            elif (re.match(r'^正确答案[：:]\s*[A-D]', text) or
                  re.match(r'^答案[：:]\s*[A-D]', text) or
                  re.match(r'^答[：:]\s*[A-D]', text)):
                # 提取答案内容
                answer_match = re.search(r'[：:]\s*([A-D]+)', text)
                if answer_match:
                    current_question["answer"] = answer_match.group(1)
                else:
                    current_question["answer"] = text
                    
                # 判断题型
                if len(current_question["options"]) > 1:
                    if len(current_question["answer"]) > 1:
                        current_question["type"] = "multiple_choice"
                    else:
                        current_question["type"] = "single_choice"
                else:
                    current_question["type"] = "single_choice"
                    
            # 检测判断题答案
            elif (re.match(r'^正确答案[：:]\s*[对错正确错误]', text) or
                  re.match(r'^答案[：:]\s*[对错正确错误]', text)):
                # 提取答案内容
                answer_match = re.search(r'[：:]\s*([对错正确错误]+)', text)
                if answer_match:
                    current_question["answer"] = answer_match.group(1)
                else:
                    current_question["answer"] = text
                current_question["type"] = "judgment"
                
            # 检测考生答案行（需要清理）
            elif re.match(r'^考生答案[：:]', text):
                # 跳过考生答案，不保存
                continue
                
            # 检测解析行
            elif re.match(r'^解析[：:]', text):
                # 提取解析内容
                explanation_match = re.search(r'解析[：:](.*)', text)
                if explanation_match:
                    current_question["explanation"] = explanation_match.group(1).strip()
                else:
                    current_question["explanation"] = text
                
            # 检测得分行
            elif re.match(r'^得分[：:]', text):
                # 跳过得分行
                continue
                
            # 其他内容作为题目内容的一部分
            elif in_question:
                if current_question["content"]:
                    current_question["content"] += "\n" + text
                else:
                    current_question["content"] = text
        
        # 添加最后一题
        if current_question["content"] and in_question and current_question["answer"]:
            questions.append(current_question)
        
        print(f"总共提取到 {len(questions)} 道题目")
        
        # 显示前3道题目的详细信息
        for i, q in enumerate(questions[:3]):
            print(f"\n题目 {i+1}:")
            print(f"  内容: {q['content'][:100]}...")
            print(f"  选项数: {len(q['options'])}")
            print(f"  答案: {q['answer']}")
            print(f"  类型: {q['type']}")
        
        return questions
        
    except Exception as e:
        print(f"错误：{str(e)}")
        return []

if __name__ == "__main__":
    # 测试第一个文件
    file = "岗位练兵试题集-0725(1).docx"
    if os.path.exists(file):
        test_fixed_extract(file)
    else:
        print(f"文件不存在：{file}") 