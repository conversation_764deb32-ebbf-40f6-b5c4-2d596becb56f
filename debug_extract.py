#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
from docx import Document
import os

def debug_extract_questions(file_path):
    """详细调试题目提取功能"""
    print(f"正在调试文件：{file_path}")
    
    try:
        doc = Document(file_path)
        questions = []
        current_question = {"content": "", "options": [], "answer": "", "type": "unknown", "explanation": ""}
        in_question = False
        
        print(f"文档段落数：{len(doc.paragraphs)}")
        
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if not text:
                continue
                
            # 检测题目开始
            if (re.match(r'^\d+[、\.\)]', text) or
                re.match(r'^[一二三四五六七八九十]+[、\.\)]', text) or
                re.match(r'^[A-Z][、\.\)]', text)):
                
                # 保存上一题
                if current_question["content"] and in_question:
                    questions.append(current_question)
                    print(f"保存题目 {len(questions)}: {current_question['content'][:50]}...")
                
                # 开始新题目
                current_question = {
                    "content": text,
                    "options": [],
                    "answer": "",
                    "type": "unknown",
                    "explanation": ""
                }
                in_question = True
                print(f"开始新题目: {text[:50]}...")
                
            # 检测选项
            elif re.match(r'^[A-D][、\.\)]', text) and in_question:
                current_question["options"].append(text)
                print(f"  添加选项: {text}")
                
            # 检测答案
            elif (re.match(r'^正确答案[：:]\s*[A-D]', text) or
                  re.match(r'^答案[：:]\s*[A-D]', text) or
                  re.match(r'^答[：:]\s*[A-D]', text)):
                answer_match = re.search(r'[：:]\s*([A-D]+)', text)
                if answer_match:
                    current_question["answer"] = answer_match.group(1)
                else:
                    current_question["answer"] = text
                print(f"  设置答案: {current_question['answer']}")
                
            # 检测判断题答案
            elif (re.match(r'^正确答案[：:]\s*[对错正确错误]', text) or
                  re.match(r'^答案[：:]\s*[对错正确错误]', text)):
                answer_match = re.search(r'[：:]\s*([对错正确错误]+)', text)
                if answer_match:
                    current_question["answer"] = answer_match.group(1)
                else:
                    current_question["answer"] = text
                current_question["type"] = "judgment"
                print(f"  设置判断题答案: {current_question['answer']}")
                
            # 跳过考生答案和得分
            elif re.match(r'^考生答案[：:]', text) or re.match(r'^得分[：:]', text):
                print(f"  跳过: {text}")
                continue
                
            # 其他内容作为题目内容
            elif in_question:
                if current_question["content"]:
                    current_question["content"] += "\n" + text
                else:
                    current_question["content"] = text
                print(f"  添加内容: {text[:30]}...")
        
        # 添加最后一题
        if current_question["content"] and in_question:
            questions.append(current_question)
            print(f"保存最后一题: {current_question['content'][:50]}...")
        
        print(f"\n总共提取到 {len(questions)} 道题目")
        
        # 显示前3道题目的详细信息
        for i, q in enumerate(questions[:3]):
            print(f"\n题目 {i+1}:")
            print(f"  内容: {q['content'][:100]}...")
            print(f"  选项数: {len(q['options'])}")
            print(f"  答案: {q['answer']}")
            print(f"  类型: {q['type']}")
        
        return questions
        
    except Exception as e:
        print(f"错误：{str(e)}")
        return []

if __name__ == "__main__":
    # 测试第一个文件
    file = "岗位练兵试题集-0725(1).docx"
    if os.path.exists(file):
        debug_extract_questions(file)
    else:
        print(f"文件不存在：{file}") 