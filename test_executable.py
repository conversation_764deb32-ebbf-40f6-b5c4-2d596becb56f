import subprocess
import sys
import time

def test_executable():
    """测试可执行文件是否能正常启动"""
    print("测试可执行文件...")
    
    try:
        # 启动可执行文件
        process = subprocess.Popen(
            ["./dist/观测岗位练兵试题管理系统.exe"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 等待几秒钟看是否有错误
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✓ 可执行文件启动成功，没有立即崩溃")
            process.terminate()
            return True
        else:
            stdout, stderr = process.communicate()
            print("✗ 可执行文件启动失败")
            print("错误输出:", stderr.decode('utf-8', errors='ignore'))
            return False
            
    except Exception as e:
        print(f"✗ 启动可执行文件时出错: {e}")
        return False

if __name__ == "__main__":
    test_executable() 