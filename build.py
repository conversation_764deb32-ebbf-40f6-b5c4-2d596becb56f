import os
import subprocess
import sys

def build_exe():
    """使用PyInstaller打包程序"""
    print("开始打包程序...")
    
    # 安装PyInstaller（如果未安装）
    try:
        import PyInstaller
        print("PyInstaller已安装")
    except ImportError:
        print("正在安装PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
    
    # 打包命令
    cmd = [
        "pyinstaller",
        "--onefile",  # 打包为单个exe文件
        "--windowed",  # 不显示控制台窗口
        "--name=观测岗位练兵试题管理系统",  # 可执行文件名称
        "--icon=icon.ico",  # 图标文件（如果有的话）
        "--add-data=README.md;.",  # 包含README文件
        "main.py"
    ]
    
    # 如果没有图标文件，移除图标参数
    if not os.path.exists("icon.ico"):
        cmd.remove("--icon=icon.ico")
    
    print("执行打包命令:", " ".join(cmd))
    subprocess.check_call(cmd)
    
    print("打包完成！")
    print("可执行文件位置: dist/观测岗位练兵试题管理系统.exe")

if __name__ == "__main__":
    build_exe() 