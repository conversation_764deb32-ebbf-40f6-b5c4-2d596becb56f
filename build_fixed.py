import os
import subprocess
import sys

def build_exe():
    """使用修正后的spec文件打包程序"""
    print("开始打包程序（修正版）...")
    
    # 检查依赖
    print("检查依赖...")
    try:
        import numpy
        print(f"✓ numpy {numpy.__version__} 已安装")
    except ImportError:
        print("✗ numpy 未安装，正在安装...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "numpy"])
    
    try:
        import pandas
        print(f"✓ pandas {pandas.__version__} 已安装")
    except ImportError:
        print("✗ pandas 未安装，正在安装...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pandas"])
    
    # 使用修正后的spec文件打包
    print("使用 simple.spec 文件打包...")
    cmd = ["pyinstaller", "simple.spec"]
    
    print("执行打包命令:", " ".join(cmd))
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✓ 打包完成！")
        print("可执行文件位置: dist/观测岗位练兵试题管理系统.exe")
        
        # 检查文件大小
        exe_path = "dist/观测岗位练兵试题管理系统.exe"
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"文件大小: {size_mb:.1f} MB")
        
        return True
    else:
        print("✗ 打包失败")
        print("错误输出:", result.stderr)
        return False

if __name__ == "__main__":
    build_exe() 