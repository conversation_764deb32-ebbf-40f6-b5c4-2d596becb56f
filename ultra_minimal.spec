# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 排除大量不需要的模块
excludes = [
    'PyQt5', 'PyQt6', 'PySide2', 'PySide6',
    'matplotlib', 'scipy', 'sklearn', 'tensorflow', 'torch',
    'jupyter', 'IPython', 'notebook',
    'sympy', 'statsmodels', 'seaborn',
    'plotly', 'bokeh', 'altair',
    'PIL', 'Pillow',
    'cv2', 'opencv',
    'requests', 'urllib3', 'certifi',
    'babel', 'jinja2', 'markupsafe',
    'setuptools', 'pkg_resources',
    'distutils', 'site',
    'test', 'tests', 'testing',
    'unittest', 'doctest',
    'pdb', 'profile', 'cProfile',
    'multiprocessing.spawn', 'multiprocessing.forkserver',
    'concurrent.futures',
    'asyncio', 'async_timeout',
    'email', 'smtplib', 'imaplib',
    'http', 'urllib', 'ftplib',
    'ssl', 'socket', 'socketserver',
    'wsgiref', 'xmlrpc',
    'html', 'xml.sax', 'xml.dom',
    'sqlite3',
    'ctypes.wintypes',
    'win32api', 'win32con', 'win32gui', 'win32process',
    'pywin32', 'pythoncom', 'pywintypes',
    'numpy.distutils', 'numpy.f2py', 'numpy.testing',
    'pandas.plotting', 'pandas.io.formats.style',
    'pandas.tests', 'pandas._testing',
    'openpyxl.drawing', 'openpyxl.chart', 'openpyxl.pivot',
    'lxml', 'bs4', 'beautifulsoup4',
    'cryptography', 'pyOpenSSL',
    'psutil', 'subprocess',
    'logging.handlers', 'logging.config'
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'docx',
        'openpyxl',
        'pandas',
        'numpy',
        're',
        'json',
        'os',
        'datetime',
        'threading',
        'difflib',
        'gc',
        'time'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 过滤掉不需要的二进制文件
a.binaries = [x for x in a.binaries if not any(exclude in x[0].lower() for exclude in [
    'qt5', 'qt6', 'pyqt', 'pyside',
    'matplotlib', 'scipy', 'sklearn',
    'mkl_', 'libopenblas', 'libblas', 'liblapack',
    'api-ms-win', 'ucrtbase', 'vcruntime',
    'msvcp', 'msvcr', 'concrt',
    'tbb', 'intel',
    'pgf90', 'pgc', 'pgmath', 'impi', 'msmpi', 'sycl'
])]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='观测岗位练兵试题管理系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
