# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['tkinter', 'tkinter.ttk', 'tkinter.filedialog', 'tkinter.messagebox'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'PyQt5', 'PyQt6', 'matplotlib', 'scipy', 'IPython', 'jupyter', 
        'sphinx', 'numba', 'llvmlite', 'zmq', 'nacl', 'cryptography', 'bcrypt',
        'sklearn', 'skimage', 'plotly', 'bokeh', 'dask', 'distributed', 'h5py',
        'netCDF4', 'tables', 'sqlalchemy', 'psutil', 'selenium', 'sympy',
        'statsmodels', 'patsy', 'notebook', 'jupyterlab', 'nbformat', 'argon2',
        'babel', 'ruamel', 'anyio', 'pythoncom', 'win32com', 'panel', 'pyviz_comms',
        'cftime', 'markdown', 'docutils', 'lxml', 'PIL', 'Pillow', 'pyarrow',
        'fsspec', 'botocore', 'urllib3', 'charset_normalizer', 'jsonschema',
        'certifi', 'xyzservices', 'lz4', 'jinja2', 'pygments', 'cloudpickle',
        'py', 'pytest', 'tomli', 'platformdirs', 'pywintypes', 'shelve'
    ],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='观测岗位练兵试题管理系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
