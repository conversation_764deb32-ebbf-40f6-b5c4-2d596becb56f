#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
from docx import Document
import os

def test_extract_questions(file_path):
    """测试题目提取功能"""
    print(f"正在测试文件：{file_path}")
    print(f"文件大小：{os.path.getsize(file_path) / 1024:.1f} KB")
    
    try:
        doc = Document(file_path)
        print(f"文档段落数：{len(doc.paragraphs)}")
        
        # 统计各种模式的行数
        question_starts = 0
        options = 0
        answers = 0
        student_answers = 0
        scores = 0
        explanations = 0
        
        # 显示前20个非空段落
        print("\n前20个非空段落：")
        count = 0
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if text and count < 20:
                print(f"{i+1}: {text[:100]}...")
                count += 1
                
                # 统计各种模式
                if re.match(r'^\d+[、\.\)]', text):
                    question_starts += 1
                elif re.match(r'^[一二三四五六七八九十]+[、\.\)]', text):
                    question_starts += 1
                elif re.match(r'^[A-Z][、\.\)]', text):
                    question_starts += 1
                elif re.match(r'^[A-D][、\.\)]', text):
                    options += 1
                elif re.match(r'^正确答案[：:]', text):
                    answers += 1
                elif re.match(r'^考生答案[：:]', text):
                    student_answers += 1
                elif re.match(r'^得分[：:]', text):
                    scores += 1
                elif re.match(r'^解析[：:]', text):
                    explanations += 1
        
        print(f"\n统计结果：")
        print(f"题目开始：{question_starts}")
        print(f"选项：{options}")
        print(f"答案：{answers}")
        print(f"考生答案：{student_answers}")
        print(f"得分：{scores}")
        print(f"解析：{explanations}")
        
    except Exception as e:
        print(f"错误：{str(e)}")

if __name__ == "__main__":
    # 测试两个docx文件
    files = [
        "岗位练兵试题集-0725(1).docx",
        "岗位练兵试题集-无考生答案版.docx"
    ]
    
    for file in files:
        if os.path.exists(file):
            test_extract_questions(file)
            print("\n" + "="*50 + "\n")
        else:
            print(f"文件不存在：{file}") 