#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
import threading
import time

def test_progress():
    """测试进度条功能"""
    root = tk.Tk()
    root.title("进度条测试")
    root.geometry("400x200")
    
    # 创建进度窗口
    progress_window = tk.Toplevel(root)
    progress_window.title("测试进度")
    progress_window.geometry("400x200")
    progress_window.configure(bg='#f0f0f0')
    
    # 进度标签
    progress_label = tk.Label(
        progress_window,
        text="正在处理...",
        font=("微软雅黑", 12),
        bg='#f0f0f0'
    )
    progress_label.pack(pady=20)
    
    # 进度条
    progress_bar = ttk.Progressbar(
        progress_window,
        mode='determinate',
        length=300
    )
    progress_bar.pack(pady=10)
    
    # 详细信息标签
    detail_label = tk.Label(
        progress_window,
        text="准备开始...",
        font=("微软雅黑", 10),
        bg='#f0f0f0',
        wraplength=350
    )
    detail_label.pack(pady=10)
    
    def update_progress():
        """更新进度"""
        for i in range(101):
            if not progress_window.winfo_exists():
                break
            progress_bar['value'] = i
            progress_label.config(text=f"正在处理... ({i}%)")
            detail_label.config(text=f"处理第 {i} 步...")
            progress_window.update()
            time.sleep(0.05)  # 模拟处理时间
        
        if progress_window.winfo_exists():
            detail_label.config(text="处理完成！")
            progress_window.after(2000, progress_window.destroy)
    
    # 启动进度更新线程
    progress_thread = threading.Thread(target=update_progress)
    progress_thread.daemon = True
    progress_thread.start()
    
    root.mainloop()

if __name__ == "__main__":
    test_progress() 