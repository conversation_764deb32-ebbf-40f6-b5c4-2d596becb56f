@echo off
chcp 65001 >nul
title 创建观测岗位练兵试题管理系统发布包

echo 正在创建发布包...
echo.

REM 创建发布目录
if not exist "发布包" mkdir "发布包"
if not exist "发布包\观测岗位练兵试题管理系统" mkdir "发布包\观测岗位练兵试题管理系统"

REM 复制主程序
echo 复制主程序文件...
copy "dist\观测岗位练兵试题管理系统.exe" "发布包\观测岗位练兵试题管理系统\" >nul

REM 复制文档
echo 复制文档文件...
copy "README.md" "发布包\观测岗位练兵试题管理系统\" >nul
copy "用户手册.md" "发布包\观测岗位练兵试题管理系统\" >nul
copy "发布说明.md" "发布包\观测岗位练兵试题管理系统\" >nul

REM 复制示例文件
echo 复制示例文件...
if exist "示例试题.docx" copy "示例试题.docx" "发布包\观测岗位练兵试题管理系统\" >nul

echo.
echo 发布包创建完成！
echo 位置：发布包\观测岗位练兵试题管理系统\
echo.
echo 发布包内容：
dir "发布包\观测岗位练兵试题管理系统" /b

echo.
pause
