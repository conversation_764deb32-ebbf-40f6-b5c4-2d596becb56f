import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import pandas as pd
from docx import Document
import re
from difflib import SequenceMatcher
import json
from datetime import datetime
import threading
import time
import gc

class QuestionBankManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("观测岗位练兵试题管理系统")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')

        # 数据存储
        self.questions = []
        self.question_bank_file = "question_bank.json"
        self.load_question_bank()

        # 导入相关
        self.import_cancelled = False
        self.current_file_index = 0
        self.total_files = 0
        self.import_results = {"total_imported": 0, "total_duplicates": 0}
        self.progress_window = None
        self.progress_label = None
        self.progress_bar = None
        self.detail_label = None
        self.cancel_btn = None

        # 处理阶段跟踪
        self.current_stage = "准备中"
        self.stage_progress = 0
        self.total_stages = 3  # 解析、去重、保存

        self.setup_ui()

    def setup_ui(self):
        # 主标题
        title_label = tk.Label(
            self.root,
            text="观测岗位练兵试题管理系统",
            font=("微软雅黑", 16, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        # 按钮框架
        button_frame = tk.Frame(self.root, bg='#f0f0f0')
        button_frame.pack(pady=20)

        # 导入按钮
        import_btn = tk.Button(
            button_frame,
            text="导入Word文档",
            command=self.import_documents,
            font=("微软雅黑", 12),
            bg='#3498db',
            fg='white',
            relief='flat',
            padx=20,
            pady=10
        )
        import_btn.pack(side=tk.LEFT, padx=10)

        # 查看题库按钮
        view_btn = tk.Button(
            button_frame,
            text="查看题库",
            command=self.view_question_bank,
            font=("微软雅黑", 12),
            bg='#2ecc71',
            fg='white',
            relief='flat',
            padx=20,
            pady=10
        )
        view_btn.pack(side=tk.LEFT, padx=10)

        # 导出Excel按钮
        export_btn = tk.Button(
            button_frame,
            text="导出Excel",
            command=self.export_to_excel,
            font=("微软雅黑", 12),
            bg='#e67e22',
            fg='white',
            relief='flat',
            padx=20,
            pady=10
        )
        export_btn.pack(side=tk.LEFT, padx=10)

        # 开始练习按钮
        practice_btn = tk.Button(
            button_frame,
            text="开始练习",
            command=self.start_practice,
            font=("微软雅黑", 12),
            bg='#f39c12',
            fg='white',
            relief='flat',
            padx=20,
            pady=10
        )
        practice_btn.pack(pady=10)

        # 调试按钮
        debug_btn = tk.Button(
            button_frame,
            text="调试模式",
            command=self.debug_import,
            font=("微软雅黑", 10),
            bg='#9b59b6',
            fg='white',
            relief='flat',
            padx=15,
            pady=5
        )
        debug_btn.pack(pady=5)

        # 统计信息
        self.stats_label = tk.Label(
            self.root,
            text="题库统计：0 道题目",
            font=("微软雅黑", 10),
            bg='#f0f0f0',
            fg='#7f8c8d'
        )
        self.stats_label.pack(pady=10)

        self.update_stats()
        
    def import_documents(self):
        """导入docx文档 - 线程版，支持大文件"""
        files = filedialog.askopenfilenames(
            title="选择试题文档",
            filetypes=[("Word文档", "*.docx"), ("所有文件", "*.*")]
        )
        
        if not files:
            return
            
        # 创建进度窗口
        self.progress_window = tk.Toplevel(self.root)
        self.progress_window.title("导入进度")
        self.progress_window.geometry("500x250")
        self.progress_window.configure(bg='#f0f0f0')
        self.progress_window.transient(self.root)
        self.progress_window.grab_set()
        
        # 进度标签
        self.progress_label = tk.Label(
            self.progress_window,
            text="正在导入试题...",
            font=("微软雅黑", 12),
            bg='#f0f0f0'
        )
        self.progress_label.pack(pady=20)
        
        # 进度条
        self.progress_bar = tk.ttk.Progressbar(
            self.progress_window,
            mode='determinate',
            length=400
        )
        self.progress_bar.pack(pady=10)
        
        # 详细信息标签
        self.detail_label = tk.Label(
            self.progress_window,
            text="准备开始导入...",
            font=("微软雅黑", 10),
            bg='#f0f0f0',
            wraplength=450
        )
        self.detail_label.pack(pady=10)
        
        # 取消按钮
        self.cancel_btn = tk.Button(
            self.progress_window,
            text="取消导入",
            command=self.cancel_import,
            font=("微软雅黑", 10),
            bg='#e74c3c',
            fg='white',
            relief='flat',
            padx=15,
            pady=5
        )
        self.cancel_btn.pack(pady=10)
        
        # 导入状态
        self.import_cancelled = False
        self.import_results = {"total_imported": 0, "total_duplicates": 0}
        self.current_file_index = 0
        self.total_files = len(files)
        
        # 启动导入线程
        import_thread = threading.Thread(target=self._import_files_thread, args=(files,))
        import_thread.daemon = True
        import_thread.start()
        
        # 启动进度更新
        self._update_progress()
    
    def cancel_import(self):
        """取消导入"""
        self.import_cancelled = True
        if hasattr(self, 'progress_window') and self.progress_window.winfo_exists():
            self.progress_window.destroy()
    
    def _import_files_thread(self, files):
        """在后台线程中导入文件"""
        try:
            for i, file_path in enumerate(files):
                if self.import_cancelled:
                    break
                
                # 更新当前文件索引
                self.current_file_index = i
                
                try:
                    # 检查文件大小
                    file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                    
                    # 使用进度回调函数
                    questions = self.extract_questions_from_docx(file_path, self._update_progress_callback)
                    
                    if not questions:
                        continue
                    
                    imported, duplicates = self.add_questions_to_bank(questions, self._update_progress_callback)
                    self.import_results["total_imported"] += imported
                    self.import_results["total_duplicates"] += duplicates
                    
                except Exception as e:
                    print(f"处理文件 {os.path.basename(file_path)} 时出错：{str(e)}")
                    continue
            
            # 保存题库
            if not self.import_cancelled:
                # 更新阶段
                self.current_stage = "保存题库"
                self.stage_progress = 50
                
                self.save_question_bank()
                
                # 阶段完成
                self.stage_progress = 100
                
                # 在主线程中更新统计信息和关闭进度窗口
                self.root.after(0, self._finish_import)
                
        except Exception as e:
            print(f"导入过程中发生错误：{str(e)}")
            # 在主线程中显示错误信息
            self.root.after(0, lambda: self._show_error(f"导入过程中发生错误：{str(e)}"))
    
    def _update_progress_callback(self, message):
        """更新进度信息的回调函数，确保在主线程执行"""
        if hasattr(self, 'progress_window') and self.progress_window.winfo_exists():
            self.root.after(0, lambda: self.detail_label.config(text=message))
    
    def _update_progress(self):
        """定期更新进度条和标签"""
        if not hasattr(self, 'progress_window') or not self.progress_window.winfo_exists():
            return

        # 计算总体进度：基于阶段和阶段内进度
        if self.total_stages > 0:
            # 当前阶段进度 + 已完成阶段的进度
            stage_weight = 1.0 / self.total_stages
            completed_stages = 0
            
            if self.current_stage == "解析文档":
                completed_stages = 0
            elif self.current_stage == "检查重复":
                completed_stages = 1
            elif self.current_stage == "保存题库":
                completed_stages = 2
            
            overall_progress = (completed_stages * stage_weight + 
                              (self.stage_progress / 100) * stage_weight) * 100
            
            self.progress_bar['value'] = overall_progress
            self.progress_label.config(text=f"{self.current_stage}... ({int(overall_progress)}%)")
        else:
            self.progress_bar['value'] = 0
            self.progress_label.config(text="正在导入试题...")

        # Schedule next update
        if not self.import_cancelled:
            self.root.after(200, self._update_progress) # Update every 200ms
        else:
            # If cancelled, ensure window is destroyed
            if hasattr(self, 'progress_window') and self.progress_window.winfo_exists():
                self.progress_window.destroy()
    
    def extract_questions_from_docx(self, file_path, progress_callback=None):
        """从docx文件中提取题目 - 优化版，支持大文件处理"""
        try:
            # 更新阶段
            self.current_stage = "解析文档"
            self.stage_progress = 0
            
            doc = Document(file_path)
            questions = []
            current_question = {"content": "", "options": [], "answer": "", "type": "unknown", "explanation": ""}
            in_question = False
            
            total_paragraphs = len(doc.paragraphs)
            
            for i, paragraph in enumerate(doc.paragraphs):
                # 更新阶段进度
                self.stage_progress = (i / total_paragraphs) * 100
                
                # 更频繁的进度更新
                if i % 20 == 0 and progress_callback:
                    progress_callback(f"正在解析文档... {i}/{total_paragraphs} 段落 (已找到 {len(questions)} 题)")
                if i % 100 == 0: # 定期进行垃圾回收
                    gc.collect()
                
                text = paragraph.text.strip()
                if not text:
                    continue
                
                # 跳过文档头部信息
                if (text in ["窗体顶端", "窗体底端"] or 
                    text.startswith("窗体") or
                    text.startswith("2024国赛大纲") or
                    text.startswith("考生:") or
                    text.startswith("考试时长:") or
                    text.startswith("总分:") or
                    text.startswith("导出") or
                    text.startswith("只看错题") or
                    text.startswith("一、") or
                    text.startswith("二、") or
                    text.startswith("三、") or
                    text.startswith("四、") or
                    text.startswith("五、") or
                    "单选题" in text or
                    "多选题" in text or
                    "判断题" in text):
                    continue
                
                # 检测题目开始 - 只匹配数字开头的题目
                if re.match(r'^\d+[、\.\)]', text):
                    # 保存上一题
                    if current_question["content"] and in_question and current_question["answer"]:
                        questions.append(current_question)
                        if progress_callback and len(questions) % 50 == 0:
                            progress_callback(f"已找到 {len(questions)} 道题目...")
                    
                    # 开始新题目
                    current_question = {
                        "content": text,
                        "options": [],
                        "answer": "",
                        "type": "unknown",
                        "explanation": ""
                    }
                    in_question = True
                    
                # 检测选项（A-Z，支持更多格式）
                elif re.match(r'^[A-Z][、\.\)]', text) and in_question:
                    current_question["options"].append(text)
                    
                # 检测答案行 - 支持多种格式
                elif (re.match(r'^正确答案[：:]\s*[A-Z]', text) or
                      re.match(r'^答案[：:]\s*[A-Z]', text) or
                      re.match(r'^答[：:]\s*[A-Z]', text)):
                    # 提取答案内容
                    answer_match = re.search(r'[：:]\s*([A-Z]+)', text)
                    if answer_match:
                        current_question["answer"] = answer_match.group(1)
                    else:
                        current_question["answer"] = text
                        
                    # 判断题型
                    if len(current_question["options"]) > 1:
                        if len(current_question["answer"]) > 1:
                            current_question["type"] = "multiple_choice"
                        else:
                            current_question["type"] = "single_choice"
                    else:
                        current_question["type"] = "single_choice"
                        
                # 检测判断题答案
                elif (re.match(r'^正确答案[：:]\s*[对错正确错误]', text) or
                      re.match(r'^答案[：:]\s*[对错正确错误]', text)):
                    # 提取答案内容
                    answer_match = re.search(r'[：:]\s*([对错正确错误]+)', text)
                    if answer_match:
                        current_question["answer"] = answer_match.group(1)
                    else:
                        current_question["answer"] = text
                    current_question["type"] = "judgment"
                    
                # 跳过考生答案行和得分行
                elif re.match(r'^考生答案[：:]', text) or re.match(r'^得分[：:]', text):
                    continue
                    
                # 检测解析行
                elif re.match(r'^解析[：:]', text):
                    # 提取解析内容
                    explanation_match = re.search(r'[：:](.*)', text)
                    if explanation_match:
                        current_question["explanation"] = explanation_match.group(1).strip()
                    else:
                        current_question["explanation"] = text
                        
                # 其他内容作为题目内容的一部分
                elif in_question and not re.match(r'^[A-Z][、\.\)]', text):
                    # 只有当不是选项格式时才添加到题目内容
                    current_question["content"] += "\n" + text
                    
            # 添加最后一题
            if current_question["content"] and in_question and current_question["answer"]:
                questions.append(current_question)
                
            # 阶段完成
            self.stage_progress = 100
            
            return questions
            
        except Exception as e:
            print(f"解析文档时出错：{str(e)}")
            return []
    
    def add_questions_to_bank(self, new_questions, progress_callback=None):
        """添加题目到题库，去除重复 - 优化版"""
        # 更新阶段
        self.current_stage = "检查重复"
        self.stage_progress = 0
        
        imported = 0
        duplicates = 0
        
        # 创建现有题目的内容哈希集合，用于快速查找
        existing_contents = set()
        for existing_q in self.questions:
            clean_content = self.clean_question_content(existing_q["content"])
            # 使用前100个字符作为快速比较的键
            content_key = clean_content[:100].strip()
            existing_contents.add(content_key)
        
        for i, new_q in enumerate(new_questions):
            # 更新阶段进度
            self.stage_progress = (i / len(new_questions)) * 100
            
            # 每处理100道题目更新一次进度
            if i % 100 == 0 and progress_callback:
                progress_callback(f"正在检查重复题目... {i}/{len(new_questions)} ({imported} 新增, {duplicates} 重复)")
            
            # 清理题目内容（去除答案和解析）
            clean_content = self.clean_question_content(new_q["content"])
            content_key = clean_content[:100].strip()
            
            # 快速检查是否重复
            is_duplicate = False
            if content_key in existing_contents:
                # 如果快速检查发现可能重复，再进行详细比较
                for existing_q in self.questions:
                    if self.is_similar_question(clean_content, existing_q["content"]):
                        is_duplicate = True
                        duplicates += 1
                        break
            
            if not is_duplicate:
                # 创建新的题目对象
                question_obj = {
                    "id": len(self.questions) + 1,
                    "content": clean_content,
                    "options": new_q.get("options", []),
                    "answer": new_q.get("answer", ""),
                    "type": new_q.get("type", "unknown"),
                    "explanation": new_q.get("explanation", ""),
                    "import_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
                self.questions.append(question_obj)
                imported += 1
                
                # 添加到快速查找集合
                existing_contents.add(content_key)
        
        # 阶段完成
        self.stage_progress = 100
        
        return imported, duplicates
    
    def clean_question_content(self, content):
        """清理题目内容，删除考生答案和得分，保留正确答案"""
        # 去除考生答案行
        content = re.sub(r'考生答案[：:].*', '', content)
        
        # 去除得分行
        content = re.sub(r'得分[：:].*', '', content)
        
        # 去除多余空白和换行
        content = re.sub(r'\n\s*\n', '\n', content)
        content = re.sub(r'^\s+|\s+$', '', content, flags=re.MULTILINE)
        
        return content.strip()
    
    def is_similar_question(self, content1, content2, threshold=0.8):
        """检查两个题目是否相似 - 优化版"""
        # 如果内容完全相同，直接返回True
        if content1 == content2:
            return True
        
        # 如果内容长度差异很大，直接返回False
        len1, len2 = len(content1), len(content2)
        if abs(len1 - len2) > max(len1, len2) * 0.3:  # 长度差异超过30%
            return False
        
        # 简单的相似度检查
        similarity = SequenceMatcher(None, content1, content2).ratio()
        return similarity > threshold
    
    def load_question_bank(self):
        """加载题库"""
        if os.path.exists(self.question_bank_file):
            try:
                with open(self.question_bank_file, 'r', encoding='utf-8') as f:
                    self.questions = json.load(f)
            except:
                self.questions = []
        else:
            self.questions = []
    
    def save_question_bank(self):
        """保存题库"""
        with open(self.question_bank_file, 'w', encoding='utf-8') as f:
            json.dump(self.questions, f, ensure_ascii=False, indent=2)
    
    def update_stats(self):
        """更新统计信息"""
        self.stats_label.config(text=f"题库统计：{len(self.questions)} 道题目")
    
    def view_question_bank(self):
        """查看题库"""
        if not self.questions:
            messagebox.showinfo("提示", "题库为空，请先导入试题文档")
            return
        
        # 创建新窗口显示题库
        view_window = tk.Toplevel(self.root)
        view_window.title("题库查看")
        view_window.geometry("900x700")
        
        # 创建表格
        tree = ttk.Treeview(view_window, columns=("id", "content", "answer", "type"), show="headings")
        tree.heading("id", text="编号")
        tree.heading("content", text="题目内容")
        tree.heading("answer", text="答案")
        tree.heading("type", text="类型")
        
        tree.column("id", width=50)
        tree.column("content", width=500)
        tree.column("answer", width=100)
        tree.column("type", width=80)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(view_window, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # 填充数据
        for q in self.questions:
            content_preview = q["content"][:50] + "..." if len(q["content"]) > 50 else q["content"]
            tree.insert("", "end", values=(q["id"], content_preview, q["answer"], q["type"]))
        
        tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def export_to_excel(self):
        """导出题库到Excel"""
        if not self.questions:
            messagebox.showinfo("提示", "题库为空，无法导出")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="保存Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx")]
        )
        
        if not file_path:
            return
        
        try:
            # 准备数据
            data = []
            for q in self.questions:
                data.append({
                    "编号": q["id"],
                    "题目内容": q["content"],
                    "选项": "\n".join(q["options"]),
                    "答案": q["answer"],
                    "类型": q["type"],
                    "解析": q.get("explanation", ""),
                    "导入时间": q["import_time"]
                })
            
            # 创建DataFrame并导出
            df = pd.DataFrame(data)
            df.to_excel(file_path, index=False, engine='openpyxl')
            
            messagebox.showinfo("成功", f"题库已导出到：{file_path}")
            
        except Exception as e:
            messagebox.showerror("错误", f"导出失败：{str(e)}")
    
    def start_practice(self):
        """开始练习功能"""
        if not self.questions:
            messagebox.showinfo("提示", "题库为空，请先导入试题")
            return
            
        # 创建练习窗口
        practice_window = tk.Toplevel(self.root)
        practice_window.title("观测岗位练兵 - 练习模式")
        practice_window.geometry("900x700")
        practice_window.configure(bg='#f0f0f0')
        
        # 随机抽取10道题目
        import random
        practice_questions = random.sample(self.questions, min(10, len(self.questions)))
        
        # 练习状态
        self.current_question_index = 0
        self.practice_questions = practice_questions
        self.user_answers = {}
        self.show_question(practice_window, practice_questions[0])
    
    def show_question(self, window, question):
        """显示题目"""
        # 清除窗口内容
        for widget in window.winfo_children():
            widget.destroy()
            
        # 创建主框架
        main_frame = tk.Frame(window, bg='#f0f0f0')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 题目进度
        progress_label = tk.Label(
            main_frame,
            text=f"第 {self.current_question_index + 1} 题 / 共 {len(self.practice_questions)} 题",
            font=("微软雅黑", 12, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        progress_label.pack(pady=10)
        
        # 题目内容
        question_text = tk.Text(
            main_frame,
            wrap=tk.WORD,
            font=("微软雅黑", 11),
            height=8,
            bg='white',
            relief='solid',
            borderwidth=1
        )
        question_text.pack(fill=tk.X, pady=10, padx=10)
        question_text.insert(tk.END, question['content'])
        question_text.config(state=tk.DISABLED)
        
        # 答案选择区域
        answer_frame = tk.Frame(main_frame, bg='#f0f0f0')
        answer_frame.pack(fill=tk.X, pady=10)
        
        # 根据题型创建不同的选择控件
        if question.get('type') == 'judgment':
            # 判断题
            self.answer_var = tk.StringVar(value="")  # 设置空值，确保默认未选中
            tk.Radiobutton(
                answer_frame,
                text="正确",
                variable=self.answer_var,
                value="正确",
                font=("微软雅黑", 11),
                bg='#f0f0f0'
            ).pack(anchor=tk.W, pady=5)
            tk.Radiobutton(
                answer_frame,
                text="错误",
                variable=self.answer_var,
                value="错误",
                font=("微软雅黑", 11),
                bg='#f0f0f0'
            ).pack(anchor=tk.W, pady=5)
        elif question.get('type') == 'multiple_choice':
            # 多选题 - 使用复选框
            self.answer_vars = {}  # 存储每个选项的变量
            for option in question.get('options', []):
                var = tk.BooleanVar(value=False)  # 设置False，确保默认未选中
                self.answer_vars[option[0]] = var  # 使用选项字母作为键
                tk.Checkbutton(
                    answer_frame,
                    text=option,
                    variable=var,
                    font=("微软雅黑", 11),
                    bg='#f0f0f0'
                ).pack(anchor=tk.W, pady=5)
        else:
            # 单选题
            self.answer_var = tk.StringVar(value="")  # 设置空值，确保默认未选中
            for option in question.get('options', []):
                tk.Radiobutton(
                    answer_frame,
                    text=option,
                    variable=self.answer_var,
                    value=option[0],  # 取选项字母
                    font=("微软雅黑", 11),
                    bg='#f0f0f0'
                ).pack(anchor=tk.W, pady=5)
        
        # 提交按钮
        submit_btn = tk.Button(
            main_frame,
            text="提交答案",
            command=lambda: self.check_answer(window, question),
            font=("微软雅黑", 12),
            bg='#3498db',
            fg='white',
            relief='flat',
            padx=20,
            pady=10
        )
        submit_btn.pack(pady=20)
    
    def check_answer(self, window, question):
        """检查答案"""
        user_answer = ""
        if question.get('type') == 'judgment':
            user_answer = self.answer_var.get()
        elif question.get('type') == 'multiple_choice':
            # 多选题 - 收集所有选中的选项
            selected_options = []
            for option in question.get('options', []):
                if self.answer_vars[option[0]].get():
                    selected_options.append(option[0])
            user_answer = ''.join(sorted(selected_options))  # 排序后连接
        else: # single_choice
            user_answer = self.answer_var.get()

        # 检查是否选择了答案
        if not user_answer or user_answer == "":
            messagebox.showwarning("提示", "请选择一个答案")
            return
            
        # 保存用户答案
        self.user_answers[self.current_question_index] = user_answer
        
        # 显示正确答案
        correct_answer = question.get('answer', '')
        is_correct = user_answer == correct_answer
        
        # 创建结果窗口
        result_window = tk.Toplevel(window)
        result_window.title("答题结果")
        result_window.geometry("600x400")
        result_window.configure(bg='#f0f0f0')
        
        # 结果标题
        result_title = "回答正确！" if is_correct else "回答错误"
        title_color = '#27ae60' if is_correct else '#e74c3c'
        
        title_label = tk.Label(
            result_window,
            text=result_title,
            font=("微软雅黑", 16, "bold"),
            bg='#f0f0f0',
            fg=title_color
        )
        title_label.pack(pady=20)
        
        # 答案信息
        info_frame = tk.Frame(result_window, bg='#f0f0f0')
        info_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        tk.Label(
            info_frame,
            text=f"您的答案：{user_answer}",
            font=("微软雅黑", 12),
            bg='#f0f0f0'
        ).pack(anchor=tk.W, pady=5)
        
        tk.Label(
            info_frame,
            text=f"正确答案：{correct_answer}",
            font=("微软雅黑", 12),
            bg='#f0f0f0'
        ).pack(anchor=tk.W, pady=5)
        
        # 解析
        if question.get('explanation'):
            explanation_text = tk.Text(
                info_frame,
                wrap=tk.WORD,
                font=("微软雅黑", 11),
                height=6,
                bg='white',
                relief='solid',
                borderwidth=1
            )
            explanation_text.pack(fill=tk.X, pady=10)
            explanation_text.insert(tk.END, f"解析：{question['explanation']}")
            explanation_text.config(state=tk.DISABLED)
        
        # 下一题按钮
        next_btn = tk.Button(
            result_window,
            text="下一题" if self.current_question_index < len(self.practice_questions) - 1 else "完成练习",
            command=lambda: self.next_question(window, result_window),
            font=("微软雅黑", 12),
            bg='#3498db',
            fg='white',
            relief='flat',
            padx=20,
            pady=10
        )
        next_btn.pack(pady=20)
    
    def next_question(self, practice_window, result_window):
        """下一题"""
        result_window.destroy()
        self.current_question_index += 1
        
        if self.current_question_index < len(self.practice_questions):
            self.show_question(practice_window, self.practice_questions[self.current_question_index])
        else:
            self.show_final_results(practice_window)
    
    def show_final_results(self, practice_window):
        """显示最终结果"""
        # 清除窗口内容
        for widget in practice_window.winfo_children():
            widget.destroy()
            
        # 计算成绩
        correct_count = 0
        for i, question in enumerate(self.practice_questions):
            if self.user_answers.get(i) == question.get('answer'):
                correct_count += 1
        
        score = (correct_count / len(self.practice_questions)) * 100
        
        # 结果显示
        result_frame = tk.Frame(practice_window, bg='#f0f0f0')
        result_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        tk.Label(
            result_frame,
            text="练习完成！",
            font=("微软雅黑", 18, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        ).pack(pady=20)
        
        tk.Label(
            result_frame,
            text=f"正确题数：{correct_count} / {len(self.practice_questions)}",
            font=("微软雅黑", 14),
            bg='#f0f0f0'
        ).pack(pady=10)
        
        tk.Label(
            result_frame,
            text=f"得分：{score:.1f}%",
            font=("微软雅黑", 16, "bold"),
            bg='#f0f0f0',
            fg='#27ae60'
        ).pack(pady=10)
        
        # 关闭按钮
        close_btn = tk.Button(
            result_frame,
            text="关闭",
            command=practice_window.destroy,
            font=("微软雅黑", 12),
            bg='#e74c3c',
            fg='white',
            relief='flat',
            padx=20,
            pady=10
        )
        close_btn.pack(pady=20)

    def debug_import(self):
        """调试模式 - 显示导入过程的详细信息"""
        files = filedialog.askopenfilenames(
            title="选择要调试的试题文档",
            filetypes=[("Word文档", "*.docx"), ("所有文件", "*.*")]
        )
        
        if not files:
            return
        
        debug_info = []
        
        for file_path in files:
            try:
                debug_info.append(f"=== 处理文件: {os.path.basename(file_path)} ===")
                
                # 提取题目
                questions = self.extract_questions_from_docx(file_path, lambda msg: debug_info.append(msg))
                debug_info.append(f"提取到 {len(questions)} 道题目")
                
                # 显示每道题的详细信息
                for i, q in enumerate(questions, 1):
                    debug_info.append(f"\n题目 {i}:")
                    debug_info.append(f"  内容: {q['content'][:100]}...")
                    debug_info.append(f"  选项数: {len(q['options'])}")
                    debug_info.append(f"  答案: {q['answer']}")
                    debug_info.append(f"  类型: {q['type']}")
                
                # 添加到题库
                imported, duplicates = self.add_questions_to_bank(questions, lambda msg: debug_info.append(msg))
                debug_info.append(f"\n导入结果: 新增 {imported} 道, 重复 {duplicates} 道")
                
            except Exception as e:
                debug_info.append(f"错误: {str(e)}")
        
        # 显示调试信息
        debug_window = tk.Toplevel(self.root)
        debug_window.title("调试信息")
        debug_window.geometry("800x600")
        
        text_widget = tk.Text(debug_window, wrap=tk.WORD, font=("Consolas", 10))
        scrollbar = tk.Scrollbar(debug_window, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        text_widget.insert(tk.END, "\n".join(debug_info))
        text_widget.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        self.save_question_bank()
        self.update_stats()
    
    def _finish_import(self):
        """完成导入，关闭进度窗口并更新统计"""
        # 更新统计信息
        self.update_stats()
        
        # 关闭进度窗口
        if hasattr(self, 'progress_window') and self.progress_window.winfo_exists():
            self.progress_window.destroy()
        
        # 显示完成消息
        messagebox.showinfo("导入完成", 
                          f"导入完成！\n"
                          f"新增题目：{self.import_results['total_imported']} 道\n"
                          f"重复题目：{self.import_results['total_duplicates']} 道\n"
                          f"当前题库：{len(self.questions)} 道题目")
    
    def _show_error(self, error_message):
        """显示错误信息"""
        # 关闭进度窗口
        if hasattr(self, 'progress_window') and self.progress_window.winfo_exists():
            self.progress_window.destroy()
        
        # 显示错误消息
        messagebox.showerror("导入错误", error_message)
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = QuestionBankManager()
    app.run() 