import json

# 加载题库
with open('question_bank.json', 'r', encoding='utf-8') as f:
    questions = json.load(f)

print(f"题库中共有 {len(questions)} 道题目")

# 检查前几道题的格式
for i in range(min(5, len(questions))):
    q = questions[i]
    print(f"\n第{i+1}题:")
    print(f"类型: {q.get('type', 'unknown')}")
    print(f"选项: {q.get('options', [])}")
    print(f"答案: {q.get('answer', '')}")
    print(f"内容: {q.get('content', '')[:100]}...") 