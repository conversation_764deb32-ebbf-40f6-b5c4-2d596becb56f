('C:\\Users\\<USER>\\Desktop\\岗位练兵\\build\\minimal\\PYZ-00.pyz',
 [('__future__', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\_compression.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\_markupbase.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\bisect.py', 'PYMODULE'),
  ('bs4',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4.builder',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('bs4.dammit',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.formatter',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\calendar.py', 'PYMODULE'),
  ('chardet',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('cmd', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\codeop.py', 'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('contextlib', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\decimal.py', 'PYMODULE'),
  ('defusedxml',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('difflib', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\doctest.py', 'PYMODULE'),
  ('docx',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\__init__.py',
   'PYMODULE'),
  ('docx.api',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\api.py',
   'PYMODULE'),
  ('docx.blkcntnr',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\blkcntnr.py',
   'PYMODULE'),
  ('docx.dml',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\dml\\__init__.py',
   'PYMODULE'),
  ('docx.dml.color',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\dml\\color.py',
   'PYMODULE'),
  ('docx.document',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\document.py',
   'PYMODULE'),
  ('docx.drawing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\drawing\\__init__.py',
   'PYMODULE'),
  ('docx.enum',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\enum\\__init__.py',
   'PYMODULE'),
  ('docx.enum.base',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\enum\\base.py',
   'PYMODULE'),
  ('docx.enum.dml',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\enum\\dml.py',
   'PYMODULE'),
  ('docx.enum.section',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\enum\\section.py',
   'PYMODULE'),
  ('docx.enum.shape',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\enum\\shape.py',
   'PYMODULE'),
  ('docx.enum.style',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\enum\\style.py',
   'PYMODULE'),
  ('docx.enum.table',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\enum\\table.py',
   'PYMODULE'),
  ('docx.enum.text',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\enum\\text.py',
   'PYMODULE'),
  ('docx.exceptions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\exceptions.py',
   'PYMODULE'),
  ('docx.image',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\image\\__init__.py',
   'PYMODULE'),
  ('docx.image.bmp',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\image\\bmp.py',
   'PYMODULE'),
  ('docx.image.constants',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\image\\constants.py',
   'PYMODULE'),
  ('docx.image.exceptions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\image\\exceptions.py',
   'PYMODULE'),
  ('docx.image.gif',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\image\\gif.py',
   'PYMODULE'),
  ('docx.image.helpers',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\image\\helpers.py',
   'PYMODULE'),
  ('docx.image.image',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\image\\image.py',
   'PYMODULE'),
  ('docx.image.jpeg',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\image\\jpeg.py',
   'PYMODULE'),
  ('docx.image.png',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\image\\png.py',
   'PYMODULE'),
  ('docx.image.tiff',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\image\\tiff.py',
   'PYMODULE'),
  ('docx.opc',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\opc\\__init__.py',
   'PYMODULE'),
  ('docx.opc.constants',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\opc\\constants.py',
   'PYMODULE'),
  ('docx.opc.coreprops',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\opc\\coreprops.py',
   'PYMODULE'),
  ('docx.opc.exceptions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\opc\\exceptions.py',
   'PYMODULE'),
  ('docx.opc.oxml',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\opc\\oxml.py',
   'PYMODULE'),
  ('docx.opc.package',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\opc\\package.py',
   'PYMODULE'),
  ('docx.opc.packuri',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\opc\\packuri.py',
   'PYMODULE'),
  ('docx.opc.part',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\opc\\part.py',
   'PYMODULE'),
  ('docx.opc.parts',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\opc\\parts\\__init__.py',
   'PYMODULE'),
  ('docx.opc.parts.coreprops',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\opc\\parts\\coreprops.py',
   'PYMODULE'),
  ('docx.opc.phys_pkg',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\opc\\phys_pkg.py',
   'PYMODULE'),
  ('docx.opc.pkgreader',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\opc\\pkgreader.py',
   'PYMODULE'),
  ('docx.opc.pkgwriter',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\opc\\pkgwriter.py',
   'PYMODULE'),
  ('docx.opc.rel',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\opc\\rel.py',
   'PYMODULE'),
  ('docx.opc.shared',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\opc\\shared.py',
   'PYMODULE'),
  ('docx.opc.spec',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\opc\\spec.py',
   'PYMODULE'),
  ('docx.oxml',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\__init__.py',
   'PYMODULE'),
  ('docx.oxml.coreprops',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\coreprops.py',
   'PYMODULE'),
  ('docx.oxml.document',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\document.py',
   'PYMODULE'),
  ('docx.oxml.drawing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\drawing.py',
   'PYMODULE'),
  ('docx.oxml.exceptions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\exceptions.py',
   'PYMODULE'),
  ('docx.oxml.ns',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\ns.py',
   'PYMODULE'),
  ('docx.oxml.numbering',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\numbering.py',
   'PYMODULE'),
  ('docx.oxml.parser',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\parser.py',
   'PYMODULE'),
  ('docx.oxml.section',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\section.py',
   'PYMODULE'),
  ('docx.oxml.settings',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\settings.py',
   'PYMODULE'),
  ('docx.oxml.shape',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\shape.py',
   'PYMODULE'),
  ('docx.oxml.shared',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\shared.py',
   'PYMODULE'),
  ('docx.oxml.simpletypes',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\simpletypes.py',
   'PYMODULE'),
  ('docx.oxml.styles',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\styles.py',
   'PYMODULE'),
  ('docx.oxml.table',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\table.py',
   'PYMODULE'),
  ('docx.oxml.text',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\text\\__init__.py',
   'PYMODULE'),
  ('docx.oxml.text.font',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\text\\font.py',
   'PYMODULE'),
  ('docx.oxml.text.hyperlink',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\text\\hyperlink.py',
   'PYMODULE'),
  ('docx.oxml.text.pagebreak',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\text\\pagebreak.py',
   'PYMODULE'),
  ('docx.oxml.text.paragraph',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\text\\paragraph.py',
   'PYMODULE'),
  ('docx.oxml.text.parfmt',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\text\\parfmt.py',
   'PYMODULE'),
  ('docx.oxml.text.run',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\text\\run.py',
   'PYMODULE'),
  ('docx.oxml.xmlchemy',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\oxml\\xmlchemy.py',
   'PYMODULE'),
  ('docx.package',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\package.py',
   'PYMODULE'),
  ('docx.parts',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\parts\\__init__.py',
   'PYMODULE'),
  ('docx.parts.document',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\parts\\document.py',
   'PYMODULE'),
  ('docx.parts.hdrftr',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\parts\\hdrftr.py',
   'PYMODULE'),
  ('docx.parts.image',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\parts\\image.py',
   'PYMODULE'),
  ('docx.parts.numbering',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\parts\\numbering.py',
   'PYMODULE'),
  ('docx.parts.settings',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\parts\\settings.py',
   'PYMODULE'),
  ('docx.parts.story',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\parts\\story.py',
   'PYMODULE'),
  ('docx.parts.styles',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\parts\\styles.py',
   'PYMODULE'),
  ('docx.section',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\section.py',
   'PYMODULE'),
  ('docx.settings',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\settings.py',
   'PYMODULE'),
  ('docx.shape',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\shape.py',
   'PYMODULE'),
  ('docx.shared',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\shared.py',
   'PYMODULE'),
  ('docx.styles',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\styles\\__init__.py',
   'PYMODULE'),
  ('docx.styles.latent',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\styles\\latent.py',
   'PYMODULE'),
  ('docx.styles.style',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\styles\\style.py',
   'PYMODULE'),
  ('docx.styles.styles',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\styles\\styles.py',
   'PYMODULE'),
  ('docx.table',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\table.py',
   'PYMODULE'),
  ('docx.text',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\text\\__init__.py',
   'PYMODULE'),
  ('docx.text.font',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\text\\font.py',
   'PYMODULE'),
  ('docx.text.hyperlink',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\text\\hyperlink.py',
   'PYMODULE'),
  ('docx.text.pagebreak',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\text\\pagebreak.py',
   'PYMODULE'),
  ('docx.text.paragraph',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\text\\paragraph.py',
   'PYMODULE'),
  ('docx.text.parfmt',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\text\\parfmt.py',
   'PYMODULE'),
  ('docx.text.run',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\text\\run.py',
   'PYMODULE'),
  ('docx.text.tabstops',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\text\\tabstops.py',
   'PYMODULE'),
  ('docx.types',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\docx\\types.py',
   'PYMODULE'),
  ('email', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\html\\parser.py',
   'PYMODULE'),
  ('http', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\mimetypes.py', 'PYMODULE'),
  ('mkl',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\mkl\\__init__.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\numbers.py', 'PYMODULE'),
  ('numexpr',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numexpr\\__init__.py',
   'PYMODULE'),
  ('numexpr.cpuinfo',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numexpr\\cpuinfo.py',
   'PYMODULE'),
  ('numexpr.expressions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numexpr\\expressions.py',
   'PYMODULE'),
  ('numexpr.necompiler',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numexpr\\necompiler.py',
   'PYMODULE'),
  ('numexpr.tests',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numexpr\\tests\\__init__.py',
   'PYMODULE'),
  ('numexpr.tests.test_numexpr',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numexpr\\tests\\test_numexpr.py',
   'PYMODULE'),
  ('numexpr.utils',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numexpr\\utils.py',
   'PYMODULE'),
  ('numexpr.version',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numexpr\\version.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.array_api',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.groupby',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\groupby.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\pickle.py', 'PYMODULE'),
  ('pickletools',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\pickletools.py',
   'PYMODULE'),
  ('pkgutil', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pytz',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('qtpy',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\qtpy\\__init__.py',
   'PYMODULE'),
  ('qtpy.QtCore',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\qtpy\\QtCore.py',
   'PYMODULE'),
  ('qtpy.QtDataVisualization',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\qtpy\\QtDataVisualization.py',
   'PYMODULE'),
  ('qtpy.QtGui',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\qtpy\\QtGui.py',
   'PYMODULE'),
  ('qtpy.QtWidgets',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\qtpy\\QtWidgets.py',
   'PYMODULE'),
  ('qtpy._utils',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\qtpy\\_utils.py',
   'PYMODULE'),
  ('qtpy.enums_compat',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\qtpy\\enums_compat.py',
   'PYMODULE'),
  ('qtpy.sip',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\qtpy\\sip.py',
   'PYMODULE'),
  ('queue', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\random.py', 'PYMODULE'),
  ('runpy', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\signal.py', 'PYMODULE'),
  ('six',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\socketserver.py',
   'PYMODULE'),
  ('soupsieve',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\threading.py', 'PYMODULE'),
  ('threadpoolctl',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\threadpoolctl.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('traitlets',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\traitlets\\__init__.py',
   'PYMODULE'),
  ('traitlets._version',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\traitlets\\_version.py',
   'PYMODULE'),
  ('traitlets.traitlets',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\traitlets\\traitlets.py',
   'PYMODULE'),
  ('traitlets.utils',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\traitlets\\utils\\__init__.py',
   'PYMODULE'),
  ('traitlets.utils.bunch',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\traitlets\\utils\\bunch.py',
   'PYMODULE'),
  ('traitlets.utils.decorators',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\traitlets\\utils\\decorators.py',
   'PYMODULE'),
  ('traitlets.utils.descriptions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\traitlets\\utils\\descriptions.py',
   'PYMODULE'),
  ('traitlets.utils.getargspec',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\traitlets\\utils\\getargspec.py',
   'PYMODULE'),
  ('traitlets.utils.importstring',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\traitlets\\utils\\importstring.py',
   'PYMODULE'),
  ('traitlets.utils.sentinel',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\traitlets\\utils\\sentinel.py',
   'PYMODULE'),
  ('traitlets.utils.warnings',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\traitlets\\utils\\warnings.py',
   'PYMODULE'),
  ('tty', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('uuid', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc.server',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\xmlrpc\\server.py',
   'PYMODULE'),
  ('yaml',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\Anaconda3\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'C:\\Users\\<USER>\\Anaconda3\\Lib\\zipimport.py', 'PYMODULE')])
