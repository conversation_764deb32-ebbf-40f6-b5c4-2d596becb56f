# 示例试题文档说明

由于无法直接创建docx文件，请按照以下格式创建您的试题文档：

## 推荐格式：

```
1. 下列哪个是Python的内置数据类型？
A. list
B. array
C. vector
D. matrix
答案：A
解析：Python的内置数据类型包括list、tuple、dict等，array不是内置类型。

2. 在Python中，如何定义一个空列表？
A. list = []
B. list = list()
C. list = None
D. list = ""
答案：A
解析：在Python中，使用方括号[]可以定义一个空列表。

3. 以下哪个函数用于获取列表长度？
A. len()
B. size()
C. length()
D. count()
答案：A
解析：Python中使用len()函数获取列表、字符串等对象的长度。

4. 在Python中，字符串是不可变的吗？
A. 是
B. 否
C. 取决于Python版本
D. 取决于操作系统
答案：A
解析：在Python中，字符串是不可变对象，一旦创建就不能修改。

5. 以下哪个是Python的注释符号？
A. //
B. /*
C. #
D. --
答案：C
解析：Python使用#作为单行注释符号。
```

## 注意事项：

1. 题目编号要清晰（1. 2. 3. 或 1) 2) 3)）
2. 选项使用A. B. C. D.格式
3. 答案行格式：答案：X
4. 解析行格式：解析：内容
5. 每个题目之间用空行分隔

## 测试步骤：

1. 按照上述格式创建docx文档
2. 运行程序：python main.py
3. 点击"导入试题文档"按钮
4. 选择您创建的docx文件
5. 查看导入结果 