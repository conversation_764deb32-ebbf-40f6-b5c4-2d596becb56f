# 观测岗位练兵试题管理系统 - 用户手册

## 📋 系统简介

观测岗位练兵试题管理系统是一个专为观测岗位练兵考试设计的试题管理工具，支持导入、管理、练习试题。

## 🚀 快速开始

### 方法一：直接运行（推荐）
1. 双击 `启动程序.bat` 文件
2. 程序会自动检查Python环境并安装依赖
3. 程序启动后即可使用

### 方法二：手动运行
1. 确保已安装Python 3.7或更高版本
2. 打开命令提示符，进入程序目录
3. 运行命令：`pip install -r requirements.txt`
4. 运行命令：`python main.py`

### 方法三：使用打包版本
1. 下载 `观测岗位练兵试题管理系统.exe`
2. 双击运行即可（无需安装Python）

## 📖 功能说明

### 1. 导入试题
- **功能**：从docx文档中提取试题
- **操作**：点击"导入试题文档(docx)"按钮
- **支持格式**：单选题、多选题、判断题
- **特点**：
  - 自动去除重复题目
  - 自动清理考生答案
  - 保留正确答案和解析
  - 支持进度显示

### 2. 查看题库
- **功能**：查看当前题库中的所有题目
- **操作**：点击"查看当前题库"按钮
- **显示内容**：题目内容、选项、答案、解析

### 3. 导出题库
- **功能**：将题库导出为Excel文件
- **操作**：点击"导出题库到Excel"按钮
- **格式**：包含题目、选项、答案、解析的Excel表格

### 4. 开始练习
- **功能**：随机抽取题目进行练习
- **操作**：点击"开始练习"按钮
- **特点**：
  - 随机抽取10道题目
  - 支持单选题、多选题、判断题
  - 答题后立即显示正确答案
  - 显示答题解析
  - 练习结束后显示总成绩

## 📝 使用说明

### 导入试题
1. 准备docx格式的试题文档
2. 点击"导入试题文档(docx)"按钮
3. 选择要导入的docx文件
4. 等待导入完成（会显示进度条）
5. 导入完成后会显示统计信息

### 开始练习
1. 确保题库中已有题目
2. 点击"开始练习"按钮
3. 系统会随机抽取10道题目
4. 选择答案后点击"提交答案"
5. 查看答题结果和正确答案
6. 点击"下一题"继续练习
7. 练习结束后查看总成绩

### 查看题库
1. 点击"查看当前题库"按钮
2. 可以浏览所有已导入的题目
3. 查看题目详情、选项、答案、解析

### 导出题库
1. 点击"导出题库到Excel"按钮
2. 选择保存位置
3. 系统会生成包含所有题目信息的Excel文件

## ⚠️ 注意事项

### 文档格式要求
- 文档格式：docx（Word文档）
- 题目编号：以数字开头，如"1、"、"2、"等
- 选项格式：以字母开头，如"A、"、"B、"等
- 答案格式：以"正确答案："或"答案："开头
- 解析格式：以"解析："开头

### 系统要求
- **操作系统**：Windows 7/8/10/11
- **Python版本**：3.7或更高版本
- **内存要求**：建议4GB以上
- **存储空间**：至少100MB可用空间

### 常见问题

**Q：导入时程序无响应？**
A：大文件导入需要时间，请耐心等待进度条完成。

**Q：题目显示不正确？**
A：请检查docx文档格式是否符合要求。

**Q：练习时无法选择答案？**
A：确保题目类型正确识别，单选题显示单选按钮，多选题显示复选框。

**Q：程序启动失败？**
A：请确保已正确安装Python和依赖包。

## 🛠️ 技术支持

如有问题，请联系技术支持：
- 邮箱：<EMAIL>
- 版本：v1.0.0
- 更新日期：2024年

## 📄 许可证

本软件采用MIT许可证，详见LICENSE文件。 